/* Completed Projects Page Styles */
.completed-projects-page {
  padding: var(--space-24) 0;
  min-height: calc(100vh - 80px);
  background: var(--primary-bg);
}

/* Page Header */
.page-header {
  text-align: center;
  margin-bottom: var(--space-16);
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
}

.page-title {
  font-size: var(--text-6xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
  background: var(--accent-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  font-size: var(--text-xl);
  color: var(--text-secondary);
  line-height: 1.6;
  max-width: 700px;
  margin: 0 auto var(--space-8);
}

/* Completion Statistics */
.completion-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-6);
  margin-top: var(--space-8);
}

.stat-item {
  text-align: center;
  padding: var(--space-4);
  background: var(--glass-bg);
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-primary);
}

/* Filter Buttons */
.filter-buttons {
  display: flex;
  justify-content: center;
  gap: var(--space-4);
  margin-bottom: var(--space-12);
  flex-wrap: wrap;
}

.filter-btn {
  padding: var(--space-3) var(--space-6);
  background: transparent;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  font-size: var(--text-sm);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
}

.filter-btn:hover {
  background: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

.filter-btn.active {
  background: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

/* Completed Projects Grid */
.completed-projects-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-8);
  margin-bottom: var(--space-16);
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
}

/* Error and No Results States */
.error-message,
.no-projects {
  text-align: center;
  padding: var(--space-16) var(--space-8);
  color: var(--text-secondary);
}

.no-projects h3 {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-3);
}

.no-projects p {
  font-size: var(--text-base);
  max-width: 400px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Results Info */
.results-info {
  text-align: center;
  margin-top: var(--space-8);
  color: var(--text-muted);
  font-size: var(--text-sm);
}

.stat-number {
  display: block;
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--accent-primary);
  margin-bottom: var(--space-1);
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Filter Section */
.filter-section {
  margin-bottom: var(--space-12);
  text-align: center;
}

.filter-buttons {
  display: flex;
  justify-content: center;
  gap: var(--space-3);
  flex-wrap: wrap;
}

.filter-btn {
  padding: var(--space-3) var(--space-6);
  background: var(--glass-bg);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-full);
  color: var(--text-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-fast);
}

.filter-btn:hover,
.filter-btn.active {
  background: var(--accent-primary);
  color: var(--primary-bg);
  border-color: var(--accent-primary);
}

/* Completed Grid */
.completed-grid {
   display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--space-8);
  margin-bottom: var(--space-16);
  justify-content: center; /* Center the grid when there are fewer cards */
  max-width: 1200px;        /* Optional: limit the overall width */
  margin-left: auto;
  margin-right: auto;
  padding: 0 1rem; 
}

/* Completed Card */
.completed-card {
  background: var(--secondary-bg);
  border-radius: var(--radius-2xl);
  overflow: hidden;
  border: 1px solid var(--border-primary);
  transition: var(--transition-normal);
  cursor: pointer;
}

.completed-card:hover {
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-glow);
}

/* Card Image */
.card-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition-normal);
}

.completed-card:hover .card-image img {
  transform: scale(1.05);
}

.card-overlay {
  position: absolute;
  top: 0%;
  right: var(--space-4);
  display: flex;
  gap: var(--space-2);
  align-items: flex-start;
}

.completion-year {
  background: var(--accent-primary);
  color: var(--primary-bg);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  font-weight: 600;
}

.satisfaction-badge {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-primary);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-lg);
  text-align: center;
  color: var(--text-primary);
}

.satisfaction-badge span {
  display: block;
  font-size: var(--text-sm);
  font-weight: 700;
  color: var(--accent-secondary);
}

.satisfaction-badge small {
  font-size: var(--text-xs);
  color: var(--text-secondary);
}

/* Card Content */
.card-content {
  padding: var(--space-6);
}

.card-header {
  margin-bottom: var(--space-4);
}

.card-title {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.card-location {
  color: var(--accent-primary);
  font-size: var(--text-base);
  font-weight: 500;
  margin-bottom: var(--space-1);
}

.card-category {
  display: inline-block;
  background: var(--glass-bg);
  color: var(--text-secondary);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  border: 1px solid var(--border-secondary);
}

.card-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-4);
}

/* Card Details */
.card-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.detail-label {
  font-size: var(--text-xs);
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
}

.detail-value {
  font-size: var(--text-base);
  color: var(--text-primary);
  font-weight: 600;
}

/* Card Awards */
.card-awards {
  margin-bottom: var(--space-6);
}

.award-badge {
  display: block;
  background: var(--accent-secondary);
  color: var(--primary-bg);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-lg);
  font-size: var(--text-xs);
  font-weight: 500;
  margin-bottom: var(--space-2);
}

/* View Details Button */
.view-details-btn {
  width: 100%;
  padding: var(--space-3) var(--space-6);
  background: var(--accent-gradient);
  border: none;
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
}

.view-details-btn:hover {
  background: var(--accent-gradient-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Project Modal */
.project-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  padding: var(--space-4);
}

.modal-content {
  background: var(--secondary-bg);
  border-radius: var(--radius-2xl);
  max-width: 900px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  border: 1px solid var(--border-primary);
}

.modal-close {
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  background: var(--accent-secondary);
  color: var(--primary-bg);
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: var(--text-2xl);
  cursor: pointer;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-fast);
}

.modal-close:hover {
  background: var(--text-primary);
}

.modal-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-2);
  padding: var(--space-6);
  padding-bottom: 0;
}

.modal-gallery img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: var(--radius-lg);
}

.modal-info {
  padding: var(--space-6);
}

.modal-info h3 {
  font-size: var(--text-3xl);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.modal-location {
  color: var(--accent-secondary);
  font-size: var(--text-lg);
  font-weight: 500;
  margin-bottom: var(--space-4);
}

.modal-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-6);
}

.modal-features {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-3);
}

.modal-feature {
  background: var(--accent-secondary);
  color: var(--primary-bg);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .completed-projects-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-6);
  }
}

@media (max-width: 768px) {
  .completed-projects-page {
    padding: var(--space-16) 0;
  }

  .completed-projects-grid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .filter-buttons {
    gap: var(--space-2);
    margin-bottom: var(--space-8);
  }

  .filter-btn {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-xs);
  }
  
  .page-title {
    font-size: var(--text-4xl);
  }
  
  .page-subtitle {
    font-size: var(--text-lg);
  }
  
  .completion-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-4);
  }
  
  .completed-grid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }
  
  .completed-card {
    max-width: 500px;
    margin: 0 auto;
  }
  
  .filter-buttons {
    gap: var(--space-2);
  }
  
  .filter-btn {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-sm);
  }
  
  .modal-content {
    margin: var(--space-4);
    max-height: 85vh;
  }
  
  .modal-gallery {
    grid-template-columns: 1fr;
    padding: var(--space-4);
  }
  
  .modal-info {
    padding: var(--space-4);
  }
  
  .modal-info h3 {
    font-size: var(--text-2xl);
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: var(--text-3xl);
  }

  .completion-stats {
    grid-template-columns: 1fr;
  }

  .filter-buttons {
    flex-direction: column;
    align-items: center;
  }

  .filter-btn {
    width: 200px;
  }

  .card-details {
    grid-template-columns: 1fr;
  }
}

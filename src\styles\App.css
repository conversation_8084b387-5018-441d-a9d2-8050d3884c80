/* Import Admin Components Styles */
@import './AdminComponents.css';

/* App Component Styles */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
}

/* Page Styles */
.page-content {
  flex: 1;
  padding-top: 80px; /* Account for fixed header */
  min-height: calc(100vh - 80px);
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--secondary-bg);
}

::-webkit-scrollbar-thumb {
  background: var(--accent-primary);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-primary);
}

/* Loading animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

/* Parallax effect */
.parallax {
  transform: translateZ(0);
  will-change: transform;
}

/* Selection styles */
::selection {
  background: var(--accent-primary);
  color: var(--primary-bg);
}

::-moz-selection {
  background: var(--accent-primary);
  color: var(--primary-bg);
}

/* Focus styles */
*:focus {
  
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .header,
  .footer {
    display: none;
  }

  .main-content {
    padding-top: 0;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  :root {
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --accent-primary: #00ffff;
    --border-primary: #666666;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

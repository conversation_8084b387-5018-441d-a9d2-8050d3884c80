import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useSelector, useDispatch } from 'react-redux';
import { 
  FiHome, 
  FiFolder, 
  FiUsers, 
  FiSettings, 
  FiLogOut,
  FiPlus,
  FiEdit,
  FiTrash2,
  <PERSON>Eye
} from 'react-icons/fi';
import { selectIsAuthenticated, selectIsAdmin, selectCurrentUser } from '../store/slices/authSlice';
import { logout } from '../store/slices/authSlice';
import { useLogoutMutation } from '../store/api/authApi';
import { useGetProjectsQuery, useDeleteProjectMutation } from '../store/api/projectsApi';
import { useToast } from '../hooks/useToast';
import LoadingSpinner from '../components/LoadingSpinner';
import SEOHead from '../components/SEOHead';
import '../styles/AdminDashboard.css';
import { BiBuilding } from 'react-icons/bi';

const AdminDashboard = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedProjects, setSelectedProjects] = useState([]);

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { showSuccess, showError } = useToast();

  const isAuthenticated = useSelector(selectIsAuthenticated);
  const isAdmin = useSelector(selectIsAdmin);
  const currentUser = useSelector(selectCurrentUser);

  const [logoutMutation] = useLogoutMutation();
  const { data: projectsData, isLoading: projectsLoading, refetch: refetchProjects } = useGetProjectsQuery({ limit: 100 });
  const [deleteProject] = useDeleteProjectMutation();

  const projects = projectsData?.projects || [];

  // Redirect if not authenticated or not admin
  useEffect(() => {
    if (!isAuthenticated || !isAdmin) {
      navigate('/admin', { replace: true });
    }
  }, [isAuthenticated, isAdmin, navigate]);

  const handleLogout = async () => {
    try {
      await logoutMutation().unwrap();
      dispatch(logout());
      showSuccess('Logged out successfully');
      navigate('/admin', { replace: true });
    } catch (error) {
      dispatch(logout());
      navigate('/admin', { replace: true });
    }
  };

  const handleDeleteProject = async (projectId) => {
    if (window.confirm('Are you sure you want to delete this project?')) {
      try {
        await deleteProject(projectId).unwrap();
        showSuccess('Project deleted successfully');
        refetchProjects();
      } catch (error) {
        showError(error?.data?.message || 'Failed to delete project');
      }
    }
  };

  const menuItems = [
    { id: 'overview', label: 'Overview', icon: FiHome },
    { id: 'projects', label: 'Projects', icon: FiFolder },
    { id: 'apartments', label: 'Apartments', icon: BiBuilding },
    { id: 'leads', label: 'Leads', icon: FiUsers },
    { id: 'settings', label: 'Settings', icon: FiSettings },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  if (!isAuthenticated || !isAdmin) {
    return <LoadingSpinner size="large" text="Checking authentication..." />;
  }

  return (
    <div className="admin-dashboard">
      <SEOHead
        title="Admin Dashboard - Elite Estate"
        description="Admin dashboard for managing Elite Estate projects, apartments, and leads"
        keywords={['admin', 'dashboard', 'management', 'projects']}
      />

      {/* Sidebar */}
      <aside className="admin-sidebar">
        <div className="sidebar-header">
          <h2>Admin Panel</h2>
          <p>Welcome, {currentUser?.name}</p>
        </div>

        <nav className="sidebar-nav">
          {menuItems.map((item) => (
            <button
              key={item.id}
              className={`nav-item ${activeTab === item.id ? 'active' : ''}`}
              onClick={() => setActiveTab(item.id)}
            >
              <item.icon size={20} />
              <span>{item.label}</span>
            </button>
          ))}
        </nav>

        <div className="sidebar-footer">
          <button className="logout-btn" onClick={handleLogout}>
            <FiLogOut size={20} />
            <span>Logout</span>
          </button>
        </div>
      </aside>

      {/* Main Content */}
      <main className="admin-main">
        <div className="admin-header">
          <h1>
            {menuItems.find(item => item.id === activeTab)?.label || 'Dashboard'}
          </h1>
        </div>

        <div className="admin-content">
          {activeTab === 'overview' && (
            <motion.div
              className="overview-section"
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              <div className="stats-grid">
                <motion.div className="stat-card" variants={itemVariants}>
                  <div className="stat-icon">
                    <FiFolder />
                  </div>
                  <div className="stat-info">
                    <h3>{projects.length}</h3>
                    <p>Total Projects</p>
                  </div>
                </motion.div>

                <motion.div className="stat-card" variants={itemVariants}>
                  <div className="stat-icon">
                    <BiBuilding />
                  </div>
                  <div className="stat-info">
                    <h3>{projects.filter(p => p.status === 'ongoing').length}</h3>
                    <p>Ongoing Projects</p>
                  </div>
                </motion.div>

                <motion.div className="stat-card" variants={itemVariants}>
                  <div className="stat-icon">
                    <FiUsers />
                  </div>
                  <div className="stat-info">
                    <h3>{projects.filter(p => p.status === 'completed').length}</h3>
                    <p>Completed Projects</p>
                  </div>
                </motion.div>
              </div>
            </motion.div>
          )}

          {activeTab === 'projects' && (
            <motion.div
              className="projects-section"
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              <div className="section-header">
                <h2>Manage Projects</h2>
                <button className="add-btn">
                  <FiPlus size={18} />
                  Add New Project
                </button>
              </div>

              {projectsLoading ? (
                <LoadingSpinner size="large" text="Loading projects..." />
              ) : (
                <div className="projects-table">
                  <table>
                    <thead>
                      <tr>
                        <th>Project Name</th>
                        <th>Status</th>
                        <th>Category</th>
                        <th>Location</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {projects.map((project) => (
                        <tr key={project._id}>
                          <td>
                            <div className="project-info">
                              <strong>{project.title}</strong>
                              <small>{project.shortDescription}</small>
                            </div>
                          </td>
                          <td>
                            <span className={`status-badge ${project.status}`}>
                              {project.status}
                            </span>
                          </td>
                          <td>{project.category}</td>
                          <td>{project.location}</td>
                          <td>
                            <div className="action-buttons">
                              <button className="action-btn view">
                                <FiEye size={16} />
                              </button>
                              <button className="action-btn edit">
                                <FiEdit size={16} />
                              </button>
                              <button 
                                className="action-btn delete"
                                onClick={() => handleDeleteProject(project._id)}
                              >
                                <FiTrash2 size={16} />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </motion.div>
          )}

          {activeTab === 'apartments' && (
            <div className="apartments-section">
              <h2>Apartments Management</h2>
              <p>Apartment management features coming soon...</p>
            </div>
          )}

          {activeTab === 'leads' && (
            <div className="leads-section">
              <h2>Leads Management</h2>
              <p>Lead management features coming soon...</p>
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="settings-section">
              <h2>System Settings</h2>
              <p>Settings management features coming soon...</p>
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default AdminDashboard;

/* Contact Page Styles */
.contact-page {
  min-height: calc(100vh - 80px);
  background: var(--primary-bg);
}

/* Hero Section */
.contact-hero {
  padding: var(--space-24) 0 var(--space-16) 0;
  background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
  text-align: center;
}

.contact-hero .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.hero-content h1 {
  font-size: var(--text-6xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
  background: var(--accent-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-content p {
  font-size: var(--text-xl);
  color: var(--text-secondary);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

/* Contact Content */
.contact-content {
  padding: var(--space-16) 0;
}

.contact-content .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-12);
  align-items: start;
}

/* Contact Info Section */
.contact-info {
  background: var(--secondary-bg);
  padding: var(--space-8);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--border-primary);
}

.contact-info h2 {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.contact-info > p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-8);
}

/* Contact Methods */
.contact-methods {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.contact-method {
  display: flex;
  align-items: flex-start;
  gap: var(--space-4);
  padding: var(--space-4);
  background: var(--primary-bg);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-secondary);
  transition: var(--transition-fast);
}

.contact-method:hover {
  border-color: var(--accent-primary);
  box-shadow: 0 4px 12px rgba(0, 212, 255, 0.1);
}

.contact-method.whatsapp:hover {
  border-color: #25D366;
  box-shadow: 0 4px 12px rgba(37, 211, 102, 0.1);
}

.method-icon {
  font-size: var(--text-2xl);
  color: var(--accent-primary);
  flex-shrink: 0;
  margin-top: var(--space-1);
}

.contact-method.whatsapp .method-icon {
  color: #25D366;
}

.method-details h4 {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.method-details a {
  display: block;
  color: var(--text-secondary);
  text-decoration: none;
  margin-bottom: var(--space-1);
  transition: var(--transition-fast);
}

.method-details a:hover {
  color: var(--accent-primary);
}

.method-details p {
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

/* Contact Form Container */
.contact-form-container {
  background: var(--secondary-bg);
  padding: var(--space-8);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--border-primary);
}

.contact-form-container h2 {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-6);
}

/* Form Styles */
.contact-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.form-group label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: var(--text-sm);
}

.form-group input,
.form-group textarea {
  padding: var(--space-3);
  background: var(--primary-bg);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-size: var(--text-base);
  transition: var(--transition-fast);
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
}

.form-group input.error,
.form-group textarea.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.error-text {
  color: #ef4444;
  font-size: var(--text-sm);
  margin-top: var(--space-1);
}

/* Submit Button */
.submit-btn {
  padding: var(--space-4) var(--space-8);
  background: var(--accent-gradient);
  border: none;
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-size: var(--text-lg);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  margin-top: var(--space-4);
}

.submit-btn:hover:not(:disabled) {
  background: var(--accent-gradient-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .contact-hero {
    padding: var(--space-16) 0 var(--space-12) 0;
  }

  .hero-content h1 {
    font-size: var(--text-4xl);
  }

  .hero-content p {
    font-size: var(--text-lg);
  }

  .contact-content {
    padding: var(--space-12) 0;
  }

  .contact-grid {
    grid-template-columns: 1fr;
    gap: var(--space-8);
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .contact-info,
  .contact-form-container {
    padding: var(--space-6);
  }

  .contact-info h2,
  .contact-form-container h2 {
    font-size: var(--text-2xl);
  }

  .contact-methods {
    gap: var(--space-4);
  }

  .contact-method {
    flex-direction: column;
    text-align: center;
    gap: var(--space-3);
  }

  .method-icon {
    margin-top: 0;
  }
}

@media (max-width: 480px) {
  .hero-content h1 {
    font-size: var(--text-3xl);
  }

  .contact-info,
  .contact-form-container {
    padding: var(--space-4);
  }

  .submit-btn {
    padding: var(--space-3) var(--space-6);
    font-size: var(--text-base);
  }

  .contact-method {
    padding: var(--space-3);
  }
}

/* Admin Dashboard Styles */
.admin-dashboard {
  display: flex;
  min-height: 100vh;
  background: var(--primary-bg);
}

/* Sidebar */
.admin-sidebar {
  width: 280px;
  background: var(--secondary-bg);
  border-right: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100vh;
  overflow-y: auto;
}

.sidebar-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
}

.sidebar-header h2 {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.sidebar-header p {
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

.sidebar-nav {
  flex: 1;
  padding: var(--space-4) 0;
}

.nav-item {
  width: 100%;
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-6);
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: var(--text-base);
  cursor: pointer;
  transition: var(--transition-fast);
  text-align: left;
}

.nav-item:hover {
  background: var(--glass-bg);
  color: var(--text-primary);
}

.nav-item.active {
  background: var(--accent-primary);
  color: var(--text-primary);
}

.sidebar-footer {
  padding: var(--space-4) var(--space-6);
  border-top: 1px solid var(--border-primary);
}

.logout-btn {
  width: 100%;
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  background: none;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  font-size: var(--text-base);
  cursor: pointer;
  transition: var(--transition-fast);
}

.logout-btn:hover {
  background: #ef4444;
  color: white;
  border-color: #ef4444;
}

/* Main Content */
.admin-main {
  flex: 1;
  margin-left: 280px;
  display: flex;
  flex-direction: column;
}

.admin-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
  background: var(--secondary-bg);
}

.admin-header h1 {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--text-primary);
}

.admin-content {
  flex: 1;
  padding: var(--space-6);
  overflow-y: auto;
}

/* Overview Section */
.overview-section {
  max-width: 1200px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.stat-card {
  background: var(--secondary-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  display: flex;
  align-items: center;
  gap: var(--space-4);
  transition: var(--transition-fast);
}

.stat-card:hover {
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-glow);
}

.stat-icon {
  width: 60px;
  height: 60px;
  background: var(--accent-gradient);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  font-size: var(--text-2xl);
}

.stat-info h3 {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.stat-info p {
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

/* Projects Section */
.projects-section {
  max-width: 1400px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
}

.section-header h2 {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
}

.add-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  background: var(--accent-primary);
  color: var(--text-primary);
  border: none;
  border-radius: var(--radius-lg);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-fast);
}

.add-btn:hover {
  background: var(--accent-secondary);
  transform: translateY(-2px);
}

/* Projects Table */
.projects-table {
  background: var(--secondary-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  overflow: hidden;
}

.projects-table table {
  width: 100%;
  border-collapse: collapse;
}

.projects-table th,
.projects-table td {
  padding: var(--space-4);
  text-align: left;
  border-bottom: 1px solid var(--border-primary);
}

.projects-table th {
  background: var(--glass-bg);
  color: var(--text-primary);
  font-weight: 600;
  font-size: var(--text-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.projects-table td {
  color: var(--text-secondary);
}

.projects-table tr:hover {
  background: var(--glass-bg);
}

.project-info strong {
  color: var(--text-primary);
  display: block;
  margin-bottom: var(--space-1);
}

.project-info small {
  color: var(--text-muted);
  font-size: var(--text-xs);
}

.status-badge {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.upcoming {
  background: rgba(255, 170, 0, 0.2);
  color: #ffaa00;
}

.status-badge.ongoing {
  background: rgba(0, 212, 255, 0.2);
  color: var(--accent-primary);
}

.status-badge.completed {
  background: rgba(0, 255, 136, 0.2);
  color: #00ff88;
}

.action-buttons {
  display: flex;
  gap: var(--space-2);
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-fast);
}

.action-btn.view {
  background: rgba(0, 212, 255, 0.2);
  color: var(--accent-primary);
}

.action-btn.edit {
  background: rgba(255, 170, 0, 0.2);
  color: #ffaa00;
}

.action-btn.delete {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.action-btn:hover {
  transform: scale(1.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-sidebar {
    width: 100%;
    position: relative;
    height: auto;
  }

  .admin-main {
    margin-left: 0;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    gap: var(--space-4);
    align-items: stretch;
  }

  .projects-table {
    overflow-x: auto;
  }

  .projects-table table {
    min-width: 600px;
  }
}

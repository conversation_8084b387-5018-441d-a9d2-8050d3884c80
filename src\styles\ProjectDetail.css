/* Project Detail Page Styles */
.project-detail {
  min-height: 100vh;
}

/* Loading and Error States */
.project-detail-loading,
.project-detail-error {
  min-height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-content {
  text-align: center;
  max-width: 400px;
}

.error-content h2 {
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.error-content p {
  color: var(--text-secondary);
  margin-bottom: var(--space-6);
}

.back-to-projects {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  background: var(--accent-gradient);
  color: white;
  text-decoration: none;
  border-radius: var(--radius-lg);
  font-weight: 600;
  transition: var(--transition-normal);
}

.back-to-projects:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Hero Section */
.project-hero {
  position: relative;
  height: 100vh;
  min-height: 600px;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    rgba(0, 0, 0, 0.6) 100%
  );
}

.hero-content {
  position: relative;
  z-index: 2;
  width: 100%;
}

.hero-info {
  max-width: 600px;
  color: white;
}

.back-link {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-weight: 500;
  margin-bottom: var(--space-6);
  transition: var(--transition-normal);
}

.back-link:hover {
  color: white;
  transform: translateX(-4px);
}

.project-badges {
  display: flex;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.status-badge {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 600;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.featured-badge {
  background: var(--accent-gradient);
  color: white;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.project-title {
  font-size: clamp(1.5rem, 5vw, 1.5rem);
  font-weight: 800;
  margin-bottom: var(--space-4);
  line-height: 1.1;
}

.project-location {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-lg);
  margin-bottom: var(--space-6);
  color: rgba(255, 255, 255, 0.9);
}

.project-description {
  font-size: var(--text-lg);
  line-height: 1.6;
  margin-bottom: var(--space-8);
  color: rgba(255, 255, 255, 0.9);
}

.hero-actions {
  display: flex;
  gap: var(--space-4);
  flex-wrap: wrap;
}

.contact-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: 600;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: var(--transition-normal);
  font-size: var(--text-base);
}

.contact-btn.primary {
  background: var(--accent-gradient);
  color: white;
}

.contact-btn.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.contact-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.contact-btn.primary:hover {
  background: var(--accent-gradient-hover);
}

.contact-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Project Details Section */
.project-details-section {
  padding: var(--space-20) 0;
  background: var(--primary-bg);
}

.details-grid {
  display: grid;
  gap: var(--space-8);
  max-width: 1200px;
  margin: 0 auto;
}

.detail-card {
  background: var(--card-bg);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-sm);
}

.detail-card h3 {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-6);
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.detail-card h3::before {
  content: '';
  width: 4px;
  height: 24px;
  background: var(--accent-gradient);
  border-radius: 2px;
}

.detail-items {
  display: grid;
  gap: var(--space-6);
}

.detail-item {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.detail-item svg {
  color: var(--accent-primary);
  flex-shrink: 0;
}

.detail-item .label {
  font-size: var(--text-sm);
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
  display: block;
  margin-bottom: var(--space-1);
}

.detail-item .value {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
}

/* Progress Bar */
.progress-info {
  flex: 1;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--border-primary);
  border-radius: 4px;
  overflow: hidden;
  margin: var(--space-2) 0;
}

.progress-fill {
  height: 100%;
  background: var(--accent-gradient);
  border-radius: 4px;
  transition: width 1s ease-in-out;
}

/* Amenities Grid */
.amenities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-4);
}

.amenity-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  padding: var(--space-4);
  background: var(--glass-bg);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
}

.amenity-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--accent-gradient);
  border-radius: var(--radius-lg);
  flex-shrink: 0;
}

.amenity-item h4 {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.amenity-item p {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: 1.5;
}

/* Features List */
.features-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-3);
}

.feature-tag {
  padding: var(--space-2) var(--space-4);
  background: var(--glass-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--text-primary);
  transition: var(--transition-normal);
}

.feature-tag:hover {
  background: var(--accent-primary);
  color: white;
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .project-hero {
    height: 80vh;
    min-height: 500px;
  }
  
  .hero-info {
    padding: 0 var(--space-4);
  }
  
  .hero-actions {
    flex-direction: column;
  }
  
  .contact-btn {
    justify-content: center;
  }
  
  .project-details-section {
    padding: var(--space-16) 0;
  }
  
  .detail-card {
    padding: var(--space-6);
  }
  
  .amenities-grid {
    grid-template-columns: 1fr;
  }
  
  .detail-items {
    gap: var(--space-4);
  }
}

@media (max-width: 480px) {
  .project-hero {
    height: 70vh;
    min-height: 400px;
  }
  
  .project-badges {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .detail-card {
    padding: var(--space-4);
  }
  
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }
}
